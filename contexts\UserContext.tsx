import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface UserState {
  name: string;
  isOnline: boolean;
}

interface UserContextType {
  user: UserState;
  updateUserName: (name: string) => void;
  toggleOnlineStatus: () => void;
  setOnlineStatus: (isOnline: boolean) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserContextProviderProps {
  children: ReactNode;
}

export const UserContextProvider: React.FC<UserContextProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserState>({
    name: '',
    isOnline: false,
  });

  const updateUserName = (name: string) => {
    setUser(prev => ({ ...prev, name }));
  };

  const toggleOnlineStatus = () => {
    setUser(prev => ({ ...prev, isOnline: !prev.isOnline }));
  };

  const setOnlineStatus = (isOnline: boolean) => {
    setUser(prev => ({ ...prev, isOnline }));
  };

  const value: UserContextType = {
    user,
    updateUserName,
    toggleOnlineStatus,
    setOnlineStatus,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export const useUserContext = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserContextProvider');
  }
  return context;
};
