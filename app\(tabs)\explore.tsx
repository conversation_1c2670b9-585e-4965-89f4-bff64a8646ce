import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { UserProfileForm } from '@/components/UserProfileForm';
import { useUserContext } from '@/contexts/UserContext';
import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';

export default function ContextAPIScreen() {
  const { user, updateUserName, toggleOnlineStatus } = useUserContext();

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>
          Context API Implementation
        </ThemedText>
        <ThemedText style={styles.description}>
         
        </ThemedText>
      </ThemedView>

      <UserProfileForm
        currentName={user.name}
        isOnline={user.isOnline}
        onUpdateName={updateUserName}
        onToggleStatus={toggleOnlineStatus}
        title="User Profile (Context API)"
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 20,
  },
});
