import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UserState {
  name: string;
  isOnline: boolean;
}

const initialState: UserState = {
  name: '',
  isOnline: false,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    updateUserName: (state, action: PayloadAction<string>) => {
      state.name = action.payload;
    },
    toggleOnlineStatus: (state) => {
      state.isOnline = !state.isOnline;
    },
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    resetUser: (state) => {
      state.name = '';
      state.isOnline = false;
    },
  },
});

export const { updateUserName, toggleOnlineStatus, setOnlineStatus, resetUser } = userSlice.actions;
export default userSlice.reducer;
