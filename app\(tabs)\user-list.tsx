import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { UserProfileForm } from '@/components/UserProfileForm';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { toggleOnlineStatus, updateUserName } from '@/store/userSlice';
import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';

export default function ReduxScreen() {
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  const handleUpdateName = (name: string) => {
    dispatch(updateUserName(name));
  };

  const handleToggleStatus = () => {
    dispatch(toggleOnlineStatus());
  };

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>
          Redux Toolkit Implementation
        </ThemedText>
        <ThemedText style={styles.description}>
          This screen uses Redux Toolkit for state management.
          Enter your name and toggle your online status below.
        </ThemedText>
      </ThemedView>

      <UserProfileForm
        currentName={user.name}
        isOnline={user.isOnline}
        onUpdateName={handleUpdateName}
        onToggleStatus={handleToggleStatus}
        title="User Profile (Redux Toolkit)"
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 20,
  },
});
