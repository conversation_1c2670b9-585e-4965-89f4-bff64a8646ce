import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface UserProfileFormProps {
  currentName: string;
  isOnline: boolean;
  onUpdateName: (name: string) => void;
  onToggleStatus: () => void;
  title: string;
}

export const UserProfileForm: React.FC<UserProfileFormProps> = ({
  currentName,
  isOnline,
  onUpdateName,
  onToggleStatus,
  title,
}) => {
  const [name, setName] = useState(currentName);

  const handleUpdateName = () => {
    if (name.trim() === '') {
      Alert.alert('Error', 'Please enter a valid name');
      return;
    }
    onUpdateName(name.trim());
    Alert.alert('Success', 'Name updated successfully!');
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="title" style={styles.title}>
        {title}
      </ThemedText>
      
      <View style={styles.formGroup}>
        <ThemedText type="subtitle" style={styles.label}>
          Enter Your Name:
        </ThemedText>
        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Enter your name"
          placeholderTextColor="#999"
        />
        <TouchableOpacity style={styles.button} onPress={handleUpdateName}>
          <ThemedText style={styles.buttonText}>Update Name</ThemedText>
        </TouchableOpacity>
      </View>

      <View style={styles.formGroup}>
        <ThemedText type="subtitle" style={styles.label}>
          Online Status:
        </ThemedText>
        <View style={styles.statusContainer}>
          <View style={[styles.statusIndicator, { backgroundColor: isOnline ? '#4CAF50' : '#F44336' }]} />
          <ThemedText style={styles.statusText}>
            {isOnline ? 'Online' : 'Offline'}
          </ThemedText>
        </View>
        <TouchableOpacity style={styles.button} onPress={onToggleStatus}>
          <ThemedText style={styles.buttonText}>
            {isOnline ? 'Go Offline' : 'Go Online'}
          </ThemedText>
        </TouchableOpacity>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 20,
  },
  title: {
    textAlign: 'center',
    marginBottom: 10,
  },
  formGroup: {
    gap: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#000',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 16,
  },
});
