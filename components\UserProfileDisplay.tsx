import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface UserProfileDisplayProps {
  name: string;
  isOnline: boolean;
  title: string;
}

export const UserProfileDisplay: React.FC<UserProfileDisplayProps> = ({
  name,
  isOnline,
  title,
}) => {
  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        {title}
      </ThemedText>
      
      <View style={styles.profileCard}>
        <View style={styles.nameContainer}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Name:
          </ThemedText>
          <ThemedText style={styles.value}>
            {name || 'No name set'}
          </ThemedText>
        </View>
        
        <View style={styles.statusContainer}>
          <ThemedText type="defaultSemiBold" style={styles.label}>
            Status:
          </ThemedText>
          <View style={styles.statusRow}>
            <View style={[styles.statusIndicator, { backgroundColor: isOnline ? '#4CAF50' : '#F44336' }]} />
            <ThemedText style={[styles.statusText, { color: isOnline ? '#4CAF50' : '#F44336' }]}>
              {isOnline ? 'Online' : 'Offline'}
            </ThemedText>
          </View>
        </View>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8,
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
    fontSize: 18,
  },
  profileCard: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    gap: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
  },
  nameContainer: {
    gap: 4,
  },
  statusContainer: {
    gap: 4,
  },
  label: {
    fontSize: 14,
    opacity: 0.8,
  },
  value: {
    fontSize: 16,
    fontWeight: '500',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
