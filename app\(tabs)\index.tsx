import { Image } from 'expo-image';
import { StyleSheet } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { UserProfileDisplay } from '@/components/UserProfileDisplay';
import { useUserContext } from '@/contexts/UserContext';
import { useAppSelector } from '@/store/hooks';

export default function HomeScreen() {
  const contextUser = useUserContext();
  const reduxUser = useAppSelector((state) => state.user);

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title"></ThemedText>
        <HelloWave />
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Welcome to the Profile Status App!</ThemedText>
        <ThemedText>
        
        </ThemedText>
      </ThemedView>

      <UserProfileDisplay
        name={contextUser.user.name}
        isOnline={contextUser.user.isOnline}
        title="Context API State"
      />

      <UserProfileDisplay
        name={reduxUser.name}
        isOnline={reduxUser.isOnline}
        title="Redux Toolkit State"
      />

      
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 16,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
});
